/**
 * 粒子系统库页面
 */
import React, { useState, useEffect } from 'react';
import { Layout, Button, Modal, Dropdown, Menu, message, Spin, Empty, Table, Space, Input } from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  MoreOutlined,
  ExportOutlined,
  ImportOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import ParticleEditor from '../components/ParticleEditor';
import { fetchParticleSystems, createParticleSystem, updateParticleSystem, deleteParticleSystem } from '../store/particles/particlesSlice';
import './ParticleLibraryPage.less';

const { Header, Content } = Layout;
const { Search } = Input;

const ParticleLibraryPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { particleSystems, loading } = useSelector((state: RootState) => state.particles);
  
  const [searchValue, setSearchValue] = useState<string>('');
  const [editorVisible, setEditorVisible] = useState<boolean>(false);
  const [currentParticleSystemId, setCurrentParticleSystemId] = useState<string | null>(null);
  const [previewParticleSystemId, setPreviewParticleSystemId] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  
  // 加载粒子系统
  useEffect(() => {
    dispatch(fetchParticleSystems());
  }, [dispatch]);
  
  // 过滤粒子系统
  const filteredParticleSystems = particleSystems.filter(particleSystem => 
    particleSystem.name.toLowerCase().includes(searchValue.toLowerCase())
  );
  
  // 打开编辑器
  const openEditor = (particleSystemId?: string) => {
    setCurrentParticleSystemId(particleSystemId || null);
    setEditorVisible(true);
  };
  
  // 关闭编辑器
  const closeEditor = () => {
    setEditorVisible(false);
    setCurrentParticleSystemId(null);
  };
  
  // 保存粒子系统
  const handleSaveParticleSystem = (particleSystemData: any) => {
    if (particleSystemData.id) {
      dispatch(updateParticleSystem(particleSystemData));
      message.success(t('editor.particle.updateSuccess'));
    } else {
      dispatch(createParticleSystem(particleSystemData));
      message.success(t('editor.particle.createSuccess'));
    }
    closeEditor();
  };
  
  // 删除粒子系统
  const handleDeleteParticleSystem = (particleSystemId: string) => {
    Modal.confirm({
      title: t('editor.particle.confirmDelete'),
      content: t('editor.particle.confirmDeleteContent'),
      okText: t('editor.delete'),
      okType: 'danger',
      cancelText: t('editor.cancel'),
      onOk: () => {
        dispatch(deleteParticleSystem(particleSystemId));
        message.success(t('editor.particle.deleteSuccess'));
      }});
  };
  
  // 复制粒子系统
  const handleDuplicateParticleSystem = (particleSystem: any) => {
    const newParticleSystem = {
      ...particleSystem,
      id: undefined,
      name: `${particleSystem.name} (${t('editor.copy')})`};
    dispatch(createParticleSystem(newParticleSystem));
    message.success(t('editor.particle.duplicateSuccess'));
  };
  
  // 预览粒子系统
  const handlePreviewParticleSystem = (particleSystemId: string) => {
    setPreviewParticleSystemId(particleSystemId);
    setIsPlaying(true);
  };
  
  // 关闭预览
  const closePreview = () => {
    setPreviewParticleSystemId(null);
    setIsPlaying(false);
  };
  
  // 表格列定义
  const columns = [
    {
      title: t('editor.particle.name'),
      dataIndex: 'name',
      key: 'name',
      sorter: (a: any, b: any) => a.name.localeCompare(b.name)},
    {
      title: t('editor.particle.maxParticles'),
      dataIndex: 'maxParticles',
      key: 'maxParticles',
      sorter: (a: any, b: any) => a.maxParticles - b.maxParticles},
    {
      title: t('editor.particle.emitterType'),
      dataIndex: 'emitterType',
      key: 'emitterType',
      render: (type: string) => t(`editor.particle.emitterTypes.${type}`),
      filters: [
        { text: t('editor.particle.emitterTypes.point'), value: 'point' },
        { text: t('editor.particle.emitterTypes.circle'), value: 'circle' },
        { text: t('editor.particle.emitterTypes.box'), value: 'box' },
        { text: t('editor.particle.emitterTypes.sphere'), value: 'sphere' },
      ],
      onFilter: (value: string, record: any) => record.emitterType === value},
    {
      title: t('editor.particle.shape'),
      dataIndex: 'shape',
      key: 'shape',
      render: (shape: string) => t(`editor.particle.shapes.${shape}`),
      filters: [
        { text: t('editor.particle.shapes.circle'), value: 'circle' },
        { text: t('editor.particle.shapes.square'), value: 'square' },
        { text: t('editor.particle.shapes.triangle'), value: 'triangle' },
        { text: t('editor.particle.shapes.custom'), value: 'custom' },
      ],
      onFilter: (value: string, record: any) => record.shape === value},
    {
      title: t('editor.particle.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
      sorter: (a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()},
    {
      title: t('editor.actions'),
      key: 'actions',
      render: (_: any, record: any) => (
        <Space size="small">
          <Button
            type="text"
            icon={<PlayCircleOutlined />}
            onClick={() => handlePreviewParticleSystem(record.id)}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => openEditor(record.id)}
          />
          <Dropdown
            overlay={
              <Menu
                items={[
                  {
                    key: 'duplicate',
                    icon: <CopyOutlined />,
                    label: t('editor.particle.duplicate'),
                    onClick: () => handleDuplicateParticleSystem(record)},
                  {
                    key: 'export',
                    icon: <ExportOutlined />,
                    label: t('editor.particle.export'),
                    onClick: () => message.info(`导出粒子系统: ${record.name}`)},
                  {
                    key: 'delete',
                    icon: <DeleteOutlined />,
                    label: t('editor.particle.delete'),
                    danger: true,
                    onClick: () => handleDeleteParticleSystem(record.id)},
                ]}
              />
            }
            trigger={['click']}
          >
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      )},
  ];
  
  return (
    <Layout className="particle-library-page">
      <Header className="page-header">
        <div className="header-title">
          <h1>{t('editor.particle.library')}</h1>
        </div>
        <div className="header-actions">
          <Search
            placeholder={t('editor.particle.search') as string}
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
            style={{ width: 250 }}
            prefix={<SearchOutlined />}
          />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => openEditor()}>
            {t('editor.particle.create')}
          </Button>
          <Button icon={<ImportOutlined />}>
            {t('editor.particle.import')}
          </Button>
        </div>
      </Header>
      
      <Content className="page-content">
        {loading ? (
          <div className="loading-container">
            <Spin size="large" />
          </div>
        ) : filteredParticleSystems.length === 0 ? (
          <Empty
            description={
              searchValue
                ? t('editor.particle.noSearchResults')
                : t('editor.particle.noParticleSystems')
            }
          >
            <Button type="primary" icon={<PlusOutlined />} onClick={() => openEditor()}>
              {t('editor.particle.create')}
            </Button>
          </Empty>
        ) : (
          <Table
            dataSource={filteredParticleSystems}
            columns={columns}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        )}
      </Content>
      
      {/* 粒子系统编辑器模态框 */}
      <Modal
        title={null}
        open={editorVisible}
        onCancel={closeEditor}
        footer={null}
        width="80%"
        style={{ top: 20 }}
        bodyStyle={{ padding: 0, height: 'calc(90vh - 40px)' }}
      >
        <ParticleEditor
          particleSystemId={currentParticleSystemId || undefined}
          onSave={handleSaveParticleSystem}
          onCancel={closeEditor}
        />
      </Modal>
      
      {/* 粒子系统预览模态框 */}
      <Modal
        title={t('editor.particle.preview')}
        open={!!previewParticleSystemId}
        onCancel={closePreview}
        footer={[
          <Button key="close" onClick={closePreview}>
            {t('editor.close')}
          </Button>,
        ]}
        width={600}
      >
        <div className="particle-preview">
          <div className="preview-canvas-container">
            <canvas className="preview-canvas" />
          </div>
          <div className="preview-controls">
            <Button
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => setIsPlaying(!isPlaying)}
            >
              {isPlaying ? t('editor.pause') : t('editor.play')}
            </Button>
          </div>
        </div>
      </Modal>
    </Layout>
  );
};

export default ParticleLibraryPage;
